import _extends from "@babel/runtime/helpers/esm/extends";
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

import * as React from 'react';
import AccountBookOutlinedSvg from "@ant-design/icons-svg/es/asn/AccountBookOutlined";
import AntdIcon from "../components/AntdIcon";
var AccountBookOutlined = function AccountBookOutlined(props, ref) {
  return /*#__PURE__*/React.createElement(AntdIcon, _extends({}, props, {
    ref: ref,
    icon: AccountBookOutlinedSvg
  }));
};

/**![account-book](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg4MCAxODRINzEydi02NGMwLTQuNC0zLjYtOC04LThoLTU2Yy00LjQgMC04IDMuNi04IDh2NjRIMzg0di02NGMwLTQuNC0zLjYtOC04LThoLTU2Yy00LjQgMC04IDMuNi04IDh2NjRIMTQ0Yy0xNy43IDAtMzIgMTQuMy0zMiAzMnY2NjRjMCAxNy43IDE0LjMgMzIgMzIgMzJoNzM2YzE3LjcgMCAzMi0xNC4zIDMyLTMyVjIxNmMwLTE3LjctMTQuMy0zMi0zMi0zMnptLTQwIDY1NkgxODRWMjU2aDEyOHY0OGMwIDQuNCAzLjYgOCA4IDhoNTZjNC40IDAgOC0zLjYgOC04di00OGgyNTZ2NDhjMCA0LjQgMy42IDggOCA4aDU2YzQuNCAwIDgtMy42IDgtOHYtNDhoMTI4djU4NHpNNjM5LjUgNDE0aC00NWMtMyAwLTUuOCAxLjctNy4xIDQuNEw1MTQgNTYzLjhoLTIuOGwtNzMuNC0xNDUuNGE4IDggMCAwMC03LjEtNC40aC00NmMtMS4zIDAtMi43LjMtMy44IDEtMy45IDIuMS01LjMgNy0zLjIgMTAuOWw4OS4zIDE2NGgtNDguNmMtNC40IDAtOCAzLjYtOCA4djIxLjNjMCA0LjQgMy42IDggOCA4aDY1LjF2MzMuN2gtNjUuMWMtNC40IDAtOCAzLjYtOCA4djIxLjNjMCA0LjQgMy42IDggOCA4aDY1LjFWNzUyYzAgNC40IDMuNiA4IDggOGg0MS4zYzQuNCAwIDgtMy42IDgtOHYtNTMuOGg2NS40YzQuNCAwIDgtMy42IDgtOHYtMjEuM2MwLTQuNC0zLjYtOC04LThoLTY1LjR2LTMzLjdoNjUuNGM0LjQgMCA4LTMuNiA4LTh2LTIxLjNjMC00LjQtMy42LTgtOC04aC00OS4xbDg5LjMtMTY0LjFjLjYtMS4yIDEtMi41IDEtMy44LjEtNC40LTMuNC04LTcuOS04eiIgLz48L3N2Zz4=) */
var RefIcon = /*#__PURE__*/React.forwardRef(AccountBookOutlined);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'AccountBookOutlined';
}
export default RefIcon;