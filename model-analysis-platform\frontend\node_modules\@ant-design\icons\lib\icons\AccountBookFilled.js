"use strict";

var _interopRequireWildcard = require("@babel/runtime/helpers/interopRequireWildcard").default;
var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault").default;
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _extends2 = _interopRequireDefault(require("@babel/runtime/helpers/extends"));
var React = _interopRequireWildcard(require("react"));
var _AccountBookFilled = _interopRequireDefault(require("@ant-design/icons-svg/lib/asn/AccountBookFilled"));
var _AntdIcon = _interopRequireDefault(require("../components/AntdIcon"));
// GENERATE BY ./scripts/generate.ts
// DON NOT EDIT IT MANUALLY

var AccountBookFilled = function AccountBookFilled(props, ref) {
  return /*#__PURE__*/React.createElement(_AntdIcon.default, (0, _extends2.default)({}, props, {
    ref: ref,
    icon: _AccountBookFilled.default
  }));
};

/**![account-book](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg4MCAxODRINzEydi02NGMwLTQuNC0zLjYtOC04LThoLTU2Yy00LjQgMC04IDMuNi04IDh2NjRIMzg0di02NGMwLTQuNC0zLjYtOC04LThoLTU2Yy00LjQgMC04IDMuNi04IDh2NjRIMTQ0Yy0xNy43IDAtMzIgMTQuMy0zMiAzMnY2NjRjMCAxNy43IDE0LjMgMzIgMzIgMzJoNzM2YzE3LjcgMCAzMi0xNC4zIDMyLTMyVjIxNmMwLTE3LjctMTQuMy0zMi0zMi0zMnpNNjQ4LjMgNDI2LjhsLTg3LjcgMTYxLjFoNDUuN2M1LjUgMCAxMCA0LjUgMTAgMTB2MjEuM2MwIDUuNS00LjUgMTAtMTAgMTBoLTYzLjR2MjkuN2g2My40YzUuNSAwIDEwIDQuNSAxMCAxMHYyMS4zYzAgNS41LTQuNSAxMC0xMCAxMGgtNjMuNFY3NTJjMCA1LjUtNC41IDEwLTEwIDEwaC00MS4zYy01LjUgMC0xMC00LjUtMTAtMTB2LTUxLjhoLTYzLjFjLTUuNSAwLTEwLTQuNS0xMC0xMHYtMjEuM2MwLTUuNSA0LjUtMTAgMTAtMTBoNjMuMXYtMjkuN2gtNjMuMWMtNS41IDAtMTAtNC41LTEwLTEwdi0yMS4zYzAtNS41IDQuNS0xMCAxMC0xMGg0NS4ybC04OC0xNjEuMWMtMi42LTQuOC0uOS0xMC45IDQtMTMuNiAxLjUtLjggMy4xLTEuMiA0LjgtMS4yaDQ2YzMuOCAwIDcuMiAyLjEgOC45IDUuNWw3Mi45IDE0NC4zIDczLjItMTQ0LjNhMTAgMTAgMCAwMTguOS01LjVoNDVjNS41IDAgMTAgNC41IDEwIDEwIC4xIDEuNy0uMyAzLjMtMS4xIDQuOHoiIC8+PC9zdmc+) */
var RefIcon = /*#__PURE__*/React.forwardRef(AccountBookFilled);
if (process.env.NODE_ENV !== 'production') {
  RefIcon.displayName = 'AccountBookFilled';
}
var _default = exports.default = RefIcon;