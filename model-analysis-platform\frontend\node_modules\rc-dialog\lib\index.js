"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
Object.defineProperty(exports, "Panel", {
  enumerable: true,
  get: function get() {
    return _Panel.default;
  }
});
exports.default = void 0;
var _DialogWrap = _interopRequireDefault(require("./DialogWrap"));
var _Panel = _interopRequireDefault(require("./Dialog/Content/Panel"));
var _default = exports.default = _DialogWrap.default;